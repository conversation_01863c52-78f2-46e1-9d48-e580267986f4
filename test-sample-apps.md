# Test Sample Apps for Web App Manager

This document provides sample applications you can use to test the Web App Manager plugin with different frameworks.

## 1. Simple HTML App

Create a ZIP file with these contents:

### index.html
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple HTML App</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Simple HTML App</h1>
        <p>This is a basic HTML application.</p>
        <button onclick="showAlert()">Click Me!</button>
    </div>
    <script src="script.js"></script>
</body>
</html>
```

### styles.css
```css
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f0f0f0;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

button {
    background-color: #007cba;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    background-color: #005a87;
}
```

### script.js
```javascript
function showAlert() {
    alert('Hello from the Web App Manager!');
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple HTML app loaded successfully!');
});
```

## 2. React App Sample

Create a ZIP file with these contents (simulating a built React app):

### index.html
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React App</title>
    <link href="static/css/main.css" rel="stylesheet">
</head>
<body>
    <div id="root"></div>
    <script src="static/js/main.js"></script>
</body>
</html>
```

### package.json
```json
{
  "name": "sample-react-app",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  },
  "scripts": {
    "build": "react-scripts build"
  }
}
```

### static/css/main.css
```css
.App {
    text-align: center;
    padding: 20px;
}

.App-header {
    background-color: #282c34;
    padding: 20px;
    color: white;
    margin-bottom: 20px;
}

.App-logo {
    height: 40px;
    pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
    .App-logo {
        animation: App-logo-spin infinite 20s linear;
    }
}

@keyframes App-logo-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
```

### static/js/main.js
```javascript
// Simulated React app bundle
(function() {
    'use strict';
    
    function App() {
        return React.createElement('div', { className: 'App' },
            React.createElement('header', { className: 'App-header' },
                React.createElement('h1', null, 'React App'),
                React.createElement('p', null, 'This is a sample React application hosted by Web App Manager!')
            ),
            React.createElement('main', null,
                React.createElement('button', { 
                    onClick: function() { alert('React button clicked!'); }
                }, 'Click me!')
            )
        );
    }
    
    // Simulate React and ReactDOM
    window.React = {
        createElement: function(type, props, ...children) {
            const element = document.createElement(type);
            if (props) {
                Object.keys(props).forEach(key => {
                    if (key === 'className') {
                        element.className = props[key];
                    } else if (key.startsWith('on')) {
                        element.addEventListener(key.substring(2).toLowerCase(), props[key]);
                    } else {
                        element.setAttribute(key, props[key]);
                    }
                });
            }
            children.forEach(child => {
                if (typeof child === 'string') {
                    element.appendChild(document.createTextNode(child));
                } else if (child) {
                    element.appendChild(child);
                }
            });
            return element;
        }
    };
    
    window.ReactDOM = {
        render: function(element, container) {
            container.appendChild(element);
        }
    };
    
    // Render the app
    document.addEventListener('DOMContentLoaded', function() {
        const root = document.getElementById('root');
        ReactDOM.render(App(), root);
        console.log('React app loaded successfully!');
    });
})();
```

## 3. Vue.js App Sample

### index.html
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js App</title>
    <link href="css/app.css" rel="stylesheet">
</head>
<body>
    <div id="app"></div>
    <script src="js/app.js"></script>
</body>
</html>
```

### package.json
```json
{
  "name": "sample-vue-app",
  "version": "1.0.0",
  "dependencies": {
    "vue": "^3.3.0"
  },
  "scripts": {
    "build": "vue-cli-service build"
  }
}
```

### css/app.css
```css
#app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    text-align: center;
    color: #2c3e50;
    margin-top: 60px;
}

.vue-header {
    background: linear-gradient(45deg, #42b883, #35495e);
    color: white;
    padding: 20px;
    margin-bottom: 20px;
}

.vue-button {
    background-color: #42b883;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.vue-button:hover {
    background-color: #369870;
}
```

### js/app.js
```javascript
// Simulated Vue.js app
(function() {
    'use strict';
    
    // Simple Vue-like framework simulation
    function createApp(options) {
        return {
            mount: function(selector) {
                const container = document.querySelector(selector);
                const template = options.template;
                const data = options.data();
                const methods = options.methods || {};
                
                // Simple template rendering
                let html = template;
                Object.keys(data).forEach(key => {
                    html = html.replace(new RegExp(`{{\\s*${key}\\s*}}`, 'g'), data[key]);
                });
                
                container.innerHTML = html;
                
                // Bind methods
                Object.keys(methods).forEach(methodName => {
                    const elements = container.querySelectorAll(`[onclick="${methodName}()"]`);
                    elements.forEach(el => {
                        el.onclick = methods[methodName].bind({ ...data });
                    });
                });
                
                console.log('Vue.js app mounted successfully!');
            }
        };
    }
    
    const app = createApp({
        template: `
            <div>
                <div class="vue-header">
                    <h1>{{ title }}</h1>
                    <p>{{ description }}</p>
                </div>
                <main>
                    <button class="vue-button" onclick="showMessage()">{{ buttonText }}</button>
                </main>
            </div>
        `,
        data() {
            return {
                title: 'Vue.js App',
                description: 'This is a sample Vue.js application hosted by Web App Manager!',
                buttonText: 'Click me!'
            };
        },
        methods: {
            showMessage() {
                alert('Vue.js button clicked!');
            }
        }
    });
    
    document.addEventListener('DOMContentLoaded', function() {
        app.mount('#app');
    });
})();
```

## Testing Instructions

1. Create ZIP files with the above contents for each sample app
2. Go to WordPress Admin → Web Apps
3. Add a new app for each sample
4. Upload the respective ZIP file
5. Check that the framework is detected correctly
6. Visit the subdomain to test the app functionality

The plugin should automatically detect:
- HTML app as "HTML/CSS/JS"
- React app as "React ^18.2.0"
- Vue app as "Vue.js ^3.3.0"
