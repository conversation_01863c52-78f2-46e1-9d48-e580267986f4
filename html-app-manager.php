<?php
/**
 * Plugin Name: Web App Manager
 * Description: Host multiple web applications with custom subdomains. Supports HTML/CSS/JS, React, Angular, Vue.js, jQuery, Svelte, Ember.js, Preact and other modern frameworks via ZIP upload.
 * Version: 5.0
 * Author: Jermesa Studio
 * Network: true
 */

defined('ABSPATH') or die('Direct access not allowed');

class Web_App_Manager {
    private $table_name;

    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'web_apps';

        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);

        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_init', [$this, 'maybe_update_db_schema']);
        add_action('init', [$this, 'handle_subdomain_request'], 1);
        add_action('template_redirect', [$this, 'handle_subdomain_request']);

        add_action('wp_ajax_get_html_app', [$this, 'handle_ajax']);
        add_action('wp_ajax_delete_html_app', [$this, 'handle_ajax']);
        add_action('wp_ajax_fix_main_file', [$this, 'handle_ajax']);
    }

    /**
     * Check if database schema needs to be updated and update it if necessary
     */
    public function maybe_update_db_schema() {
        global $wpdb;

        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") === $this->table_name;

        if (!$table_exists) {
            // If table doesn't exist, create it
            $this->activate();
            return;
        }

        // Check if single_html_content column exists
        $single_html_content_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'single_html_content'");

        // Check if is_single_file column exists
        $is_single_file_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'is_single_file'");

        // Check if visit_count column exists
        $visit_count_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'visit_count'");

        // Check for new columns for file-based apps
        $app_type_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'app_type'");
        $file_path_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'file_path'");
        $main_file_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'main_file'");
        $framework_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'framework'");
        $framework_version_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'framework_version'");

        // Add missing columns
        if (!$single_html_content_exists) {
            $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN single_html_content longtext AFTER js_content");
        }

        if (!$is_single_file_exists) {
            $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN is_single_file tinyint(1) DEFAULT 0 AFTER single_html_content");
        }

        if (!$visit_count_exists) {
            $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN visit_count int(11) DEFAULT 0 AFTER is_single_file");
        }

        if (!$app_type_exists) {
            $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN app_type varchar(20) DEFAULT 'separate' AFTER visit_count");
        }

        if (!$file_path_exists) {
            $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN file_path varchar(255) NULL AFTER app_type");
        }

        if (!$main_file_exists) {
            $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN main_file varchar(255) NULL AFTER file_path");
        }

        if (!$framework_exists) {
            $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN framework varchar(50) DEFAULT 'html' AFTER main_file");
        }

        if (!$framework_version_exists) {
            $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN framework_version varchar(20) NULL AFTER framework");
        }

        // Migrate existing data to use new app_type field
        $this->migrate_app_types();
    }

    /**
     * Migrate existing apps to use the new app_type field
     */
    private function migrate_app_types() {
        global $wpdb;

        // Update apps that have is_single_file = 1 to app_type = 'single'
        $wpdb->query("UPDATE {$this->table_name} SET app_type = 'single' WHERE is_single_file = 1 AND app_type = 'separate'");

        // Update apps that have is_single_file = 0 to app_type = 'separate' (if not already set)
        $wpdb->query("UPDATE {$this->table_name} SET app_type = 'separate' WHERE (is_single_file = 0 OR is_single_file IS NULL) AND app_type = 'separate'");
    }

    public function activate() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE {$this->table_name} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            app_name varchar(100) NOT NULL,
            subdomain varchar(100) NOT NULL,
            html_content longtext NOT NULL,
            css_content longtext,
            js_content longtext,
            single_html_content longtext,
            is_single_file tinyint(1) DEFAULT 0,
            visit_count int(11) DEFAULT 0,
            app_type varchar(20) DEFAULT 'separate',
            file_path varchar(255) NULL,
            main_file varchar(255) NULL,
            framework varchar(50) DEFAULT 'html',
            framework_version varchar(20) NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            UNIQUE KEY subdomain (subdomain)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Create apps directory if it doesn't exist
        $this->create_apps_directory();

        // Migrate table name if needed
        $this->migrate_table_name();
    }

    /**
     * Migrate from old table name to new table name
     */
    private function migrate_table_name() {
        global $wpdb;
        $old_table = $wpdb->prefix . 'html_apps';
        $new_table = $wpdb->prefix . 'web_apps';

        // Check if old table exists and new table doesn't
        $old_exists = $wpdb->get_var("SHOW TABLES LIKE '{$old_table}'") === $old_table;
        $new_exists = $wpdb->get_var("SHOW TABLES LIKE '{$new_table}'") === $new_table;

        if ($old_exists && !$new_exists) {
            // Rename the table
            $wpdb->query("RENAME TABLE {$old_table} TO {$new_table}");

            // Migrate directory structure
            $upload_dir = wp_upload_dir();
            $old_dir = $upload_dir['basedir'] . '/html-apps';
            $new_dir = $upload_dir['basedir'] . '/web-apps';

            if (file_exists($old_dir) && !file_exists($new_dir)) {
                rename($old_dir, $new_dir);
            }
        }
    }

    /**
     * Create the apps directory for file-based apps
     */
    private function create_apps_directory() {
        $upload_dir = wp_upload_dir();
        $apps_dir = $upload_dir['basedir'] . '/web-apps';

        if (!file_exists($apps_dir)) {
            wp_mkdir_p($apps_dir);

            // Create .htaccess file to allow direct access to files with expanded file types
            $htaccess_content = "Options +Indexes\n";
            $htaccess_content .= "DirectoryIndex index.html index.htm\n";
            $htaccess_content .= "<Files ~ \"\\.(html|htm|css|js|jsx|ts|tsx|vue|json|png|jpg|jpeg|gif|svg|ico|webp|woff|woff2|ttf|eot|mp3|mp4|wav|ogg|webm|pdf|txt|xml|map)$\">\n";
            $htaccess_content .= "    Header set Cache-Control \"max-age=31536000, public\"\n";
            $htaccess_content .= "</Files>\n";
            $htaccess_content .= "# Support for modern web frameworks\n";
            $htaccess_content .= "AddType application/javascript .jsx\n";
            $htaccess_content .= "AddType application/typescript .ts\n";
            $htaccess_content .= "AddType application/typescript .tsx\n";
            $htaccess_content .= "AddType text/x-vue .vue\n";
            $htaccess_content .= "AddType application/json .map\n";

            file_put_contents($apps_dir . '/.htaccess', $htaccess_content);
        }
    }

    public function deactivate() {
        // Optionally clean up uploaded files when plugin is deactivated
        // Uncomment the following lines if you want to remove all app files on deactivation
        /*
        $upload_dir = wp_upload_dir();
        $apps_dir = $upload_dir['basedir'] . '/web-apps';
        if (file_exists($apps_dir)) {
            $this->delete_directory($apps_dir);
        }
        */
    }

    /**
     * Handle file upload and extraction
     */
    private function handle_file_upload($app_id = null) {
        // Check if ZipArchive is available
        if (!class_exists('ZipArchive')) {
            wp_die('ZipArchive PHP extension is not installed. Please contact your hosting provider to enable the ZIP extension.');
        }

        if (!isset($_FILES['app_zip']) || $_FILES['app_zip']['error'] !== UPLOAD_ERR_OK) {
            wp_die('File upload failed. Please try again.');
        }

        $file = $_FILES['app_zip'];

        // Validate file type
        $file_info = pathinfo($file['name']);
        if (strtolower($file_info['extension']) !== 'zip') {
            wp_die('Only ZIP files are allowed.');
        }

        // Validate file size (max 50MB)
        if ($file['size'] > 50 * 1024 * 1024) {
            wp_die('File size too large. Maximum allowed size is 50MB.');
        }

        $upload_dir = wp_upload_dir();
        $apps_dir = $upload_dir['basedir'] . '/web-apps';

        // Create unique directory for this app
        $app_folder = $app_id ? "app_{$app_id}" : "app_" . uniqid();
        $app_path = $apps_dir . '/' . $app_folder;

        // Remove existing directory if updating
        if ($app_id && file_exists($app_path)) {
            $this->delete_directory($app_path);
        }

        // Create app directory
        if (!wp_mkdir_p($app_path)) {
            wp_die('Failed to create app directory.');
        }

        // Extract ZIP file
        $zip = new ZipArchive();
        $zip_result = $zip->open($file['tmp_name']);

        if ($zip_result === TRUE) {
            // Extract all files
            if (!$zip->extractTo($app_path)) {
                $zip->close();
                $this->delete_directory($app_path);
                wp_die('Failed to extract files from ZIP archive. Please check file permissions.');
            }
            $zip->close();

            // Find the main HTML file and detect framework
            $main_file = $this->find_main_html_file($app_path);
            $framework_info = $this->detect_framework($app_path);

            if (!$main_file) {
                $this->delete_directory($app_path);
                wp_die('No HTML file found in the uploaded ZIP. Please ensure your app contains at least one HTML file (index.html, main.html, etc.).');
            }

            return [
                'file_path' => $app_folder,
                'main_file' => $main_file,
                'framework' => $framework_info['framework'],
                'framework_version' => $framework_info['version']
            ];
        } else {
            // Provide more specific error messages
            $error_messages = [
                ZipArchive::ER_OK => 'No error',
                ZipArchive::ER_MULTIDISK => 'Multi-disk zip archives not supported',
                ZipArchive::ER_RENAME => 'Renaming temporary file failed',
                ZipArchive::ER_CLOSE => 'Closing zip archive failed',
                ZipArchive::ER_SEEK => 'Seek error',
                ZipArchive::ER_READ => 'Read error',
                ZipArchive::ER_WRITE => 'Write error',
                ZipArchive::ER_CRC => 'CRC error',
                ZipArchive::ER_ZIPCLOSED => 'Containing zip archive was closed',
                ZipArchive::ER_NOENT => 'No such file',
                ZipArchive::ER_EXISTS => 'File already exists',
                ZipArchive::ER_OPEN => 'Can\'t open file',
                ZipArchive::ER_TMPOPEN => 'Failure to create temporary file',
                ZipArchive::ER_ZLIB => 'Zlib error',
                ZipArchive::ER_MEMORY => 'Memory allocation failure',
                ZipArchive::ER_CHANGED => 'Entry has been changed',
                ZipArchive::ER_COMPNOTSUPP => 'Compression method not supported',
                ZipArchive::ER_EOF => 'Premature EOF',
                ZipArchive::ER_INVAL => 'Invalid argument',
                ZipArchive::ER_NOZIP => 'Not a zip archive',
                ZipArchive::ER_INTERNAL => 'Internal error',
                ZipArchive::ER_INCONS => 'Zip archive inconsistent',
                ZipArchive::ER_REMOVE => 'Can\'t remove file',
                ZipArchive::ER_DELETED => 'Entry has been deleted'
            ];

            $error_message = isset($error_messages[$zip_result]) ? $error_messages[$zip_result] : 'Unknown error';
            wp_die("Failed to open ZIP file: $error_message. Please ensure the file is a valid ZIP archive.");
        }
    }

    /**
     * Find the main HTML file in the extracted directory
     */
    private function find_main_html_file($directory) {
        // Look for common main file names first (in priority order)
        $priority_names = ['index.html', 'index.htm', 'main.html', 'app.html', 'home.html'];

        foreach ($priority_names as $name) {
            if (file_exists($directory . '/' . $name)) {
                return $name;
            }
        }

        // If no priority names found, look for any HTML file in the root directory
        // but exclude admin/management files
        $exclude_patterns = ['admin', 'manage', 'config', 'setup', 'install'];
        $files = scandir($directory);
        $html_files = [];

        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..' && is_file($directory . '/' . $file)) {
                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                if (in_array($extension, ['html', 'htm'])) {
                    $filename_lower = strtolower(pathinfo($file, PATHINFO_FILENAME));

                    // Check if this is likely an admin/management file
                    $is_admin_file = false;
                    foreach ($exclude_patterns as $pattern) {
                        if (strpos($filename_lower, $pattern) !== false) {
                            $is_admin_file = true;
                            break;
                        }
                    }

                    if (!$is_admin_file) {
                        $html_files[] = $file;
                    }
                }
            }
        }

        // Return the first non-admin HTML file found
        if (!empty($html_files)) {
            return $html_files[0];
        }

        // If still no suitable HTML file found, look recursively (but prefer files in root)
        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::SELF_FIRST
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $extension = strtolower($file->getExtension());
                    if (in_array($extension, ['html', 'htm'])) {
                        $filename_lower = strtolower($file->getBasename('.html'));

                        // Skip admin files even in subdirectories
                        $is_admin_file = false;
                        foreach ($exclude_patterns as $pattern) {
                            if (strpos($filename_lower, $pattern) !== false) {
                                $is_admin_file = true;
                                break;
                            }
                        }

                        if (!$is_admin_file) {
                            // Return relative path from app directory
                            $relative_path = str_replace($directory . DIRECTORY_SEPARATOR, '', $file->getPathname());
                            return str_replace('\\', '/', $relative_path); // Normalize path separators
                        }
                    }
                }
            }
        } catch (Exception $e) {
            // If directory iteration fails, return null
            return null;
        }

        return null;
    }

    /**
     * Detect the framework used in the uploaded app
     */
    private function detect_framework($directory) {
        $framework = 'html';
        $version = '';

        // Check for package.json (Node.js based frameworks)
        $package_json_path = $directory . '/package.json';
        if (file_exists($package_json_path)) {
            $package_content = file_get_contents($package_json_path);
            $package_data = json_decode($package_content, true);

            if ($package_data && isset($package_data['dependencies'])) {
                $deps = $package_data['dependencies'];

                // React detection
                if (isset($deps['react'])) {
                    $framework = 'react';
                    $version = $deps['react'] ?? '';
                }
                // Angular detection
                elseif (isset($deps['@angular/core'])) {
                    $framework = 'angular';
                    $version = $deps['@angular/core'] ?? '';
                }
                // Vue.js detection
                elseif (isset($deps['vue'])) {
                    $framework = 'vue';
                    $version = $deps['vue'] ?? '';
                }
                // Svelte detection
                elseif (isset($deps['svelte'])) {
                    $framework = 'svelte';
                    $version = $deps['svelte'] ?? '';
                }
                // Ember.js detection
                elseif (isset($deps['ember-source'])) {
                    $framework = 'ember';
                    $version = $deps['ember-source'] ?? '';
                }
                // Preact detection
                elseif (isset($deps['preact'])) {
                    $framework = 'preact';
                    $version = $deps['preact'] ?? '';
                }
                // jQuery detection
                elseif (isset($deps['jquery'])) {
                    $framework = 'jquery';
                    $version = $deps['jquery'] ?? '';
                }
                // Next.js detection
                elseif (isset($deps['next'])) {
                    $framework = 'nextjs';
                    $version = $deps['next'] ?? '';
                }
                // Nuxt.js detection
                elseif (isset($deps['nuxt'])) {
                    $framework = 'nuxtjs';
                    $version = $deps['nuxt'] ?? '';
                }
            }
        }

        // Check for specific framework files
        if ($framework === 'html') {
            // Check for Angular specific files
            if (file_exists($directory . '/angular.json') || file_exists($directory . '/src/main.ts')) {
                $framework = 'angular';
            }
            // Check for Vue specific files
            elseif (file_exists($directory . '/vue.config.js') || file_exists($directory . '/src/main.js')) {
                $framework = 'vue';
            }
            // Check for React specific files
            elseif (file_exists($directory . '/src/App.jsx') || file_exists($directory . '/src/App.js')) {
                $framework = 'react';
            }
            // Check for Svelte specific files
            elseif (file_exists($directory . '/svelte.config.js') || file_exists($directory . '/src/App.svelte')) {
                $framework = 'svelte';
            }
        }

        return [
            'framework' => $framework,
            'version' => $version
        ];
    }

    /**
     * Delete a directory and all its contents
     */
    private function delete_directory($dir) {
        if (!file_exists($dir)) {
            return true;
        }

        $files = array_diff(scandir($dir), array('.', '..'));
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->delete_directory($path);
            } else {
                unlink($path);
            }
        }
        return rmdir($dir);
    }

    public function add_admin_menu() {
        add_menu_page(
            'Web App Manager',
            'Web Apps',
            'manage_options',
            'web-app-manager',
            [$this, 'render_admin_page'],
            'dashicons-admin-site-alt3',
            30
        );
    }

    public function register_settings() {
        register_setting('html_app_manager_settings', 'html_app_manager_options');
    }

    private function save_app() {
        if (!isset($_POST['html_app_nonce']) || !wp_verify_nonce($_POST['html_app_nonce'], 'save_html_app')) {
            wp_die('Security check failed');
        }

        // Validate required fields
        if (empty($_POST['app_name']) || empty($_POST['subdomain'])) {
            wp_die('App name and subdomain are required');
        }

        global $wpdb;

        // Make sure the database schema is up to date
        $this->maybe_update_db_schema();

        $app_type = isset($_POST['app_type']) ? $_POST['app_type'] : 'separate';
        $app_id = !empty($_POST['app_id']) ? intval($_POST['app_id']) : null;

        // Validate content based on app type
        if ($app_type === 'single' && empty($_POST['single_html_content'])) {
            wp_die('Single HTML file content is required');
        } elseif ($app_type === 'separate' && empty($_POST['html_content'])) {
            wp_die('HTML content is required');
        } elseif ($app_type === 'files' && !isset($_FILES['app_zip']) && !$app_id) {
            wp_die('ZIP file is required for file-based apps');
        }

        // Handle file upload for file-based apps
        $file_data = null;
        if ($app_type === 'files') {
            if (isset($_FILES['app_zip']) && $_FILES['app_zip']['error'] === UPLOAD_ERR_OK) {
                $file_data = $this->handle_file_upload($app_id);
            } elseif (!$app_id) {
                wp_die('ZIP file upload is required for new file-based apps');
            }
        }

        // Basic data that all apps need
        $data = [
            'app_name' => sanitize_text_field($_POST['app_name']),
            'subdomain' => sanitize_title($_POST['subdomain']),
            'app_type' => $app_type,
        ];

        // Set legacy is_single_file field for backward compatibility
        $data['is_single_file'] = ($app_type === 'single') ? 1 : 0;

        // Handle content based on app type
        if ($app_type === 'single') {
            $data['single_html_content'] = stripslashes($_POST['single_html_content']);
            $data['html_content'] = '';
            $data['css_content'] = '';
            $data['js_content'] = '';
            $data['file_path'] = null;
            $data['main_file'] = null;
        } elseif ($app_type === 'separate') {
            $data['html_content'] = stripslashes($_POST['html_content']);
            $data['css_content'] = stripslashes($_POST['css_content']);
            $data['js_content'] = stripslashes($_POST['js_content']);
            $data['single_html_content'] = '';
            $data['file_path'] = null;
            $data['main_file'] = null;
        } elseif ($app_type === 'files') {
            $data['html_content'] = '';
            $data['css_content'] = '';
            $data['js_content'] = '';
            $data['single_html_content'] = '';

            if ($file_data) {
                $data['file_path'] = $file_data['file_path'];
                $data['main_file'] = $file_data['main_file'];
                $data['framework'] = $file_data['framework'];
                $data['framework_version'] = $file_data['framework_version'];
            }
        }

        // Check if the subdomain is already in use (except for the current app being edited)
        $subdomain_check_query = "SELECT id FROM {$this->table_name} WHERE subdomain = %s";
        $subdomain_check_params = [$data['subdomain']];

        if (!empty($_POST['app_id'])) {
            $subdomain_check_query .= " AND id != %d";
            $subdomain_check_params[] = intval($_POST['app_id']);
        }

        $existing_app = $wpdb->get_var($wpdb->prepare($subdomain_check_query, $subdomain_check_params));

        if ($existing_app) {
            wp_die('This subdomain is already in use. Please choose a different subdomain.');
        }

        // Save the app
        if (!empty($_POST['app_id'])) {
            $result = $wpdb->update(
                $this->table_name,
                $data,
                ['id' => intval($_POST['app_id'])]
            );
        } else {
            $result = $wpdb->insert($this->table_name, $data);
        }

        if ($result === false) {
            wp_die('Error saving app: ' . $wpdb->last_error);
        }

        wp_redirect(admin_url('admin.php?page=html-app-manager'));
        exit;
    }

    public function render_admin_page() {
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_app'])) {
            $this->save_app();
        }

        // Get all apps
        global $wpdb;
        $apps = $wpdb->get_results("SELECT * FROM {$this->table_name} ORDER BY created_at DESC");
        ?>
        <div class="wrap">
            <h1>Web App Manager</h1>

            <div class="card" style="width: 1200px;">
                <h2>Your Apps</h2>
                <?php if ($apps) : ?>
                    <div class="apps-table-container">
                        <div class="table-responsive">
                            <table class="wp-list-table widefat" style="width: 100%;">
                                <thead>
                                    <tr>
                                        <th>App Name</th>
                                        <th>Subdomain</th>
                                        <th>Type</th>
                                        <th>Framework</th>
                                        <th>Visits</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($apps as $app) : ?>
                                        <tr>
                                            <td><?php echo esc_html($app->app_name); ?></td>
                                            <td>
                                                <a href="http://<?php echo esc_html($app->subdomain); ?>.<?php echo parse_url(network_site_url(), PHP_URL_HOST); ?>" target="_blank">
                                                    <?php echo esc_html($app->subdomain); ?>.<?php echo parse_url(network_site_url(), PHP_URL_HOST); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php
                                                if (property_exists($app, 'app_type')) {
                                                    switch($app->app_type) {
                                                        case 'single':
                                                            echo 'Single HTML File';
                                                            break;
                                                        case 'files':
                                                            echo 'File-based App';
                                                            break;
                                                        default:
                                                            echo 'Separate HTML/CSS/JS';
                                                    }
                                                } else {
                                                    echo (property_exists($app, 'is_single_file') && $app->is_single_file) ? 'Single HTML File' : 'Separate HTML/CSS/JS';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $framework = property_exists($app, 'framework') ? $app->framework : 'html';
                                                $framework_version = property_exists($app, 'framework_version') ? $app->framework_version : '';

                                                $framework_names = [
                                                    'html' => 'HTML/CSS/JS',
                                                    'react' => 'React',
                                                    'angular' => 'Angular',
                                                    'vue' => 'Vue.js',
                                                    'svelte' => 'Svelte',
                                                    'ember' => 'Ember.js',
                                                    'preact' => 'Preact',
                                                    'jquery' => 'jQuery',
                                                    'nextjs' => 'Next.js',
                                                    'nuxtjs' => 'Nuxt.js'
                                                ];

                                                $display_name = isset($framework_names[$framework]) ? $framework_names[$framework] : ucfirst($framework);
                                                echo esc_html($display_name);
                                                if ($framework_version) {
                                                    echo ' <small>(' . esc_html($framework_version) . ')</small>';
                                                }
                                                ?>
                                            </td>
                                            <td><?php echo esc_html($app->visit_count); ?></td>
                                            <td><?php echo date('M j, Y', strtotime($app->created_at)); ?></td>
                                            <td class="actions-column">
                                                <a href="#" class="button edit-app" data-id="<?php echo $app->id; ?>">Edit</a>
                                                <?php if (property_exists($app, 'app_type') && $app->app_type === 'files'): ?>
                                                    <a href="#" class="button fix-main-file" data-id="<?php echo $app->id; ?>" title="Re-detect main HTML file">Fix Main File</a>
                                                <?php endif; ?>
                                                <a href="#" class="button button-danger delete-app" data-id="<?php echo $app->id; ?>">Delete</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php else : ?>
                    <div class="no-apps-message">
                        <p>No apps found. Add your first app below.</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="card">
                <h2 id="form-title">Add New App</h2>
                <form method="post" id="app-form" enctype="multipart/form-data">
                    <input type="hidden" name="app_id" id="app_id" value="">
                    <?php wp_nonce_field('save_html_app', 'html_app_nonce'); ?>
                    <div class="form-fields">
                        <div class="form-field">
                            <label for="app_name">App Name</label>
                            <input type="text" name="app_name" required class="regular-text">
                        </div>

                        <div class="form-field">
                            <label for="subdomain">Subdomain</label>
                            <input type="text" name="subdomain" required class="regular-text">
                            <p class="description">e.g. "calendar" for calendar.yoursite.com</p>
                        </div>

                        <div class="form-field">
                            <label for="app_type">App Type</label>
                            <select name="app_type" id="app_type">
                                <option value="separate">Separate HTML/CSS/JS</option>
                                <option value="single">Single HTML File</option>
                                <option value="files">Upload ZIP File (Complex App)</option>
                            </select>
                            <p class="description">Choose how you want to add your app content</p>
                        </div>

                        <!-- Separate HTML/CSS/JS fields -->
                        <div class="separate-files-fields">
                            <div class="form-field">
                                <label for="html_content">HTML Content</label>
                                <textarea name="html_content" id="html_content" rows="10" class="large-text code"></textarea>
                                <p class="description">Required for separate HTML/CSS/JS option</p>
                            </div>

                            <div class="form-field">
                                <label for="css_content">CSS</label>
                                <textarea name="css_content" id="css_content" rows="10" class="large-text code"></textarea>
                            </div>

                            <div class="form-field">
                                <label for="js_content">JavaScript</label>
                                <textarea name="js_content" id="js_content" rows="10" class="large-text code"></textarea>
                            </div>
                        </div>

                        <!-- Single HTML file field -->
                        <div class="single-file-fields" style="display: none;">
                            <div class="form-field">
                                <label for="single_html_content">Complete HTML File</label>
                                <textarea name="single_html_content" id="single_html_content" rows="20" class="large-text code"></textarea>
                                <p class="description">Paste your complete HTML file including all HTML, CSS, and JavaScript</p>
                            </div>
                        </div>

                        <!-- File upload field -->
                        <div class="file-upload-fields" style="display: none;">
                            <div class="form-field">
                                <label for="app_zip">Upload ZIP File</label>
                                <input type="file" name="app_zip" id="app_zip" accept=".zip">
                                <p class="description">Upload a ZIP file containing your complete web application. Supports modern frameworks like React, Angular, Vue.js, and more!</p>
                                <div class="file-upload-info">
                                    <h4>Supported Frameworks & Technologies:</h4>
                                    <div class="framework-grid">
                                        <div class="framework-category">
                                            <h5>Frontend Frameworks</h5>
                                            <ul>
                                                <li>React (with JSX/TSX)</li>
                                                <li>Angular (with TypeScript)</li>
                                                <li>Vue.js (with .vue files)</li>
                                                <li>Svelte</li>
                                                <li>Ember.js</li>
                                                <li>Preact</li>
                                            </ul>
                                        </div>
                                        <div class="framework-category">
                                            <h5>Meta Frameworks</h5>
                                            <ul>
                                                <li>Next.js (React)</li>
                                                <li>Nuxt.js (Vue)</li>
                                                <li>SvelteKit</li>
                                                <li>Gatsby</li>
                                            </ul>
                                        </div>
                                        <div class="framework-category">
                                            <h5>Libraries & Tools</h5>
                                            <ul>
                                                <li>jQuery</li>
                                                <li>Bootstrap</li>
                                                <li>Tailwind CSS</li>
                                                <li>D3.js</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <h4>Supported File Types:</h4>
                                    <ul class="file-types-list">
                                        <li><strong>Web Files:</strong> .html, .htm, .css, .js, .jsx, .ts, .tsx, .vue</li>
                                        <li><strong>Images:</strong> .png, .jpg, .jpeg, .gif, .svg, .ico, .webp</li>
                                        <li><strong>Fonts:</strong> .woff, .woff2, .ttf, .eot</li>
                                        <li><strong>Media:</strong> .mp3, .mp4, .wav, .ogg, .webm</li>
                                        <li><strong>Data:</strong> .json, .xml, .csv, .txt</li>
                                        <li><strong>Build Files:</strong> .map, .md</li>
                                    </ul>
                                    <div class="upload-tips">
                                        <h4>Upload Tips:</h4>
                                        <ul>
                                            <li>For built apps (React, Angular, etc.), upload the <strong>build/dist</strong> folder contents</li>
                                            <li>Ensure your main file is named index.html, main.html, or app.html</li>
                                            <li>Include all assets (images, fonts, etc.) in the ZIP</li>
                                            <li>Maximum file size: <strong>50MB</strong></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-field submit-field">
                            <?php submit_button('Save App', 'primary', 'save_app', false); ?>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <style>
            /* Modern UI Styles */
            :root {
                --primary-color: #2271b1;
                --secondary-color: #f0f0f1;
                --border-color: #dcdcde;
                --text-color: #1d2327;
                --danger-color: #d63638;
                --success-color: #00a32a;
                --card-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                --transition: all 0.3s ease;
            }

            .wrap h1 {
                margin-bottom: 20px;
                font-weight: 500;
            }

            .card {
                background: #fff;
                padding: 25px;
                margin-bottom: 25px;
                border-radius: 4px;
                box-shadow: var(--card-shadow);
                border: 1px solid var(--border-color);
                max-width: 1000px;
            }

            .card h2 {
                margin-top: 0;
                margin-bottom: 20px;
                font-weight: 500;
                color: var(--text-color);
                border-bottom: 1px solid var(--border-color);
                padding-bottom: 10px;
            }

           /* Table styles */
            .wp-list-table {
                border-collapse: collapse;
                width: 100%;
                border: none;
                box-shadow: none;
            }

            .wp-list-table th {
                text-align: left;
                padding: 12px 10px;
                font-weight: 500;
                color: var(--text-color);
                border-bottom: 1px solid var(--border-color);
            }

            .wp-list-table td {
                padding: 12px 10px;
                vertical-align: middle;
                border-bottom: 1px solid var(--border-color);
            }

            /* Form styles */
            .form-fields {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .form-field {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .form-field label {
                font-weight: 500;
                color: var(--text-color);
            }

            .form-field input[type="text"],
            .form-field select,
            .form-field textarea {
                padding: 8px 12px;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                transition: var(--transition);
            }

            .form-field input[type="text"]:focus,
            .form-field select:focus,
            .form-field textarea:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 1px var(--primary-color);
                outline: none;
            }

            .description {
                font-size: 13px;
                color: #666;
                margin-top: 4px;
            }

            .large-text.code {
                font-family: monospace;
                white-space: pre;
                min-height: 150px;
            }

            /* File upload styles */
            .file-upload-info {
                background: #f8f9fa;
                border: 1px solid #e1e5e9;
                border-radius: 4px;
                padding: 15px;
                margin-top: 10px;
            }

            .file-upload-info h4 {
                margin-top: 15px;
                margin-bottom: 10px;
                color: var(--text-color);
                border-bottom: 1px solid #e1e5e9;
                padding-bottom: 5px;
            }

            .file-upload-info h4:first-child {
                margin-top: 0;
            }

            .file-upload-info h5 {
                margin: 10px 0 8px 0;
                color: var(--primary-color);
                font-size: 14px;
            }

            .framework-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 10px 0;
            }

            .framework-category {
                background: #fff;
                padding: 12px;
                border-radius: 4px;
                border: 1px solid #e1e5e9;
            }

            .file-upload-info ul {
                margin: 8px 0;
                padding-left: 20px;
            }

            .file-upload-info li {
                margin-bottom: 4px;
                font-size: 13px;
            }

            .file-types-list li {
                margin-bottom: 6px;
            }

            .upload-tips {
                background: #e7f3ff;
                border-left: 3px solid var(--primary-color);
                padding: 12px;
                margin-top: 15px;
                border-radius: 0 4px 4px 0;
            }

            .upload-tips h4 {
                margin-top: 0;
                border: none;
                padding: 0;
            }

            .current-file-info {
                margin-top: 5px;
                padding: 8px;
                background: #e7f3ff;
                border-left: 3px solid var(--primary-color);
                border-radius: 3px;
            }

            /* Button styles */
            .button {
                display: inline-block;
                padding: 6px 12px;
                background: #f6f7f7;
                border: 1px solid #dcdcde;
                border-radius: 3px;
                cursor: pointer;
                font-size: 13px;
                text-decoration: none;
                transition: var(--transition);
                margin-right: 5px;
            }

            .button:hover {
                background: #f0f0f1;
            }

            .button-primary {
                background: var(--primary-color);
                border-color: var(--primary-color);
                color: white;
            }

            .button-primary:hover {
                background: #135e96;
                border-color: #135e96;
            }

            .button-danger {
                color: var(--danger-color);
                border-color: var(--danger-color);
            }

            .button-danger:hover {
                background: #f6e1e1;
            }

            .submit-field {
                margin-top: 10px;
            }

            /* Table container */
            .apps-table-container {
                overflow-x: auto;
                margin-bottom: 15px;
                width: 100%;
            }

            /* No apps message */
            .no-apps-message {
                padding: 20px;
                background: var(--secondary-color);
                border-radius: 4px;
                text-align: center;
                color: #666;
            }

            /* Actions column */
            .actions-column {
                white-space: nowrap;
            }

            /* Responsive adjustments */
            @media (max-width: 782px) {
                .form-field {
                    margin-bottom: 15px;
                }

                .wp-list-table th,
                .wp-list-table td {
                    padding: 10px 8px;
                }

                .button {
                    padding: 5px 10px;
                    font-size: 12px;
                }
            }
        </style>
        <script>
        jQuery(document).ready(function($) {
            // Function to toggle between different app types
            function toggleAppType(selectedType) {
                $('.separate-files-fields').hide();
                $('.single-file-fields').hide();
                $('.file-upload-fields').hide();
                $('.current-file-info').remove();

                if (selectedType === 'single') {
                    $('.single-file-fields').show();
                } else if (selectedType === 'files') {
                    $('.file-upload-fields').show();
                } else {
                    $('.separate-files-fields').show();
                }
            }

            // Run toggle on page load
            toggleAppType($('#app_type').val());

            // Run toggle on change
            $('#app_type').change(function() {
                toggleAppType($(this).val());
            });

            // Form validation before submit
            $('#app-form').on('submit', function(e) {
                var appType = $('#app_type').val();
                var isValid = true;
                var isEditing = $('#app_id').val() !== '';

                // Clear any previous error messages
                $('.validation-error').remove();

                if (appType === 'single') {
                    if (!$('#single_html_content').val().trim()) {
                        e.preventDefault();
                        $('#single_html_content').after('<p class="validation-error" style="color: red;">Please enter the complete HTML file content</p>');
                        $('#single_html_content').focus();
                        isValid = false;
                    }
                } else if (appType === 'files') {
                    var fileInput = $('#app_zip')[0];
                    if (!isEditing && (!fileInput.files || fileInput.files.length === 0)) {
                        e.preventDefault();
                        $('#app_zip').after('<p class="validation-error" style="color: red;">Please select a ZIP file to upload</p>');
                        $('#app_zip').focus();
                        isValid = false;
                    }
                } else {
                    if (!$('#html_content').val().trim()) {
                        e.preventDefault();
                        $('#html_content').after('<p class="validation-error" style="color: red;">Please enter the HTML content</p>');
                        $('#html_content').focus();
                        isValid = false;
                    }
                }

                if (!isValid) {
                    return false;
                }

                // If we get here, the form is valid
                return true;
            });

            // Handle edit click
            $('.edit-app').click(function(e) {
                e.preventDefault();
                var appId = $(this).data('id');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'get_html_app',
                        id: appId,
                        _wpnonce: '<?php echo wp_create_nonce("html_app_action"); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#form-title').text('Edit App');
                            $('#app_id').val(response.data.id);
                            $('input[name="app_name"]').val(response.data.app_name);
                            $('input[name="subdomain"]').val(response.data.subdomain);

                            // Set the app type and show/hide appropriate fields
                            var appType = response.data.app_type || (response.data.is_single_file ? 'single' : 'separate');
                            $('#app_type').val(appType);

                            if (appType === 'single') {
                                $('#single_html_content').val(response.data.single_html_content);
                            } else if (appType === 'files') {
                                // For file-based apps, we don't populate content fields
                                // Just show the current file info
                                if (response.data.file_path && response.data.main_file) {
                                    $('#app_zip').after('<p class="current-file-info" style="color: #666; font-style: italic;">Current app: ' + response.data.file_path + ' (Main file: ' + response.data.main_file + ')</p>');
                                }
                            } else {
                                $('#html_content').val(response.data.html_content);
                                $('#css_content').val(response.data.css_content);
                                $('#js_content').val(response.data.js_content);
                            }

                            // Toggle fields based on app type
                            toggleAppType($('#app_type').val());

                            // Scroll to form
                            $('html, body').animate({
                                scrollTop: $('#app-form').offset().top - 50
                            }, 500);
                        }
                    }
                });
            });

            // Handle delete click
            $('.delete-app').click(function(e) {
                e.preventDefault();
                if (!confirm('Are you sure you want to delete this app?')) {
                    return;
                }

                var appId = $(this).data('id');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'delete_html_app',
                        id: appId,
                        _wpnonce: '<?php echo wp_create_nonce("html_app_action"); ?>'
                    },
                    success: function() {
                        location.reload();
                    }
                });
            });

            // Handle fix main file click
            $('.fix-main-file').click(function(e) {
                e.preventDefault();
                if (!confirm('This will re-detect the main HTML file for this app. Continue?')) {
                    return;
                }

                var appId = $(this).data('id');
                var button = $(this);

                button.text('Fixing...');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'fix_main_file',
                        id: appId,
                        _wpnonce: '<?php echo wp_create_nonce("html_app_action"); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Main file updated to: ' + response.data.main_file);
                            location.reload();
                        } else {
                            alert('Error: ' + response.data);
                            button.text('Fix Main File');
                        }
                    },
                    error: function() {
                        alert('Error occurred while fixing main file');
                        button.text('Fix Main File');
                    }
                });
            });
        });
        </script>
        <?php
    }

    public function handle_ajax() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'html_app_action')) {
            wp_send_json_error('Invalid nonce');
        }

        global $wpdb;

        if ($_POST['action'] === 'get_html_app') {
            $app = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE id = %d",
                intval($_POST['id'])
            ));

            if ($app) {
                // Decode HTML entities in JavaScript content
                $app->js_content = html_entity_decode($app->js_content);

                // Check if is_single_file property exists
                if (property_exists($app, 'is_single_file')) {
                    // Convert is_single_file to boolean for JavaScript
                    $app->is_single_file = (bool)$app->is_single_file;
                } else {
                    // Default to false if property doesn't exist
                    $app->is_single_file = false;
                }

                // Ensure single_html_content property exists
                if (!property_exists($app, 'single_html_content')) {
                    $app->single_html_content = '';
                }

                wp_send_json_success($app);
            }
            wp_send_json_error('App not found');
        }
        elseif ($_POST['action'] === 'delete_html_app') {
            $app_id = intval($_POST['id']);

            // Get app info before deleting
            $app = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE id = %d",
                $app_id
            ));

            // Delete app files if it's a file-based app
            if ($app && property_exists($app, 'app_type') && $app->app_type === 'files' && $app->file_path) {
                $upload_dir = wp_upload_dir();
                $app_dir = $upload_dir['basedir'] . '/web-apps/' . $app->file_path;
                $this->delete_directory($app_dir);
            }

            // Delete from database
            $wpdb->delete($this->table_name, ['id' => $app_id]);
            wp_send_json_success();
        }
        elseif ($_POST['action'] === 'fix_main_file') {
            $app_id = intval($_POST['id']);

            // Get app info
            $app = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE id = %d",
                $app_id
            ));

            if (!$app || $app->app_type !== 'files' || !$app->file_path) {
                wp_send_json_error('App not found or not a file-based app');
            }

            $upload_dir = wp_upload_dir();
            $app_dir = $upload_dir['basedir'] . '/web-apps/' . $app->file_path;

            if (!file_exists($app_dir)) {
                wp_send_json_error('App directory not found');
            }

            // Re-detect main file
            $new_main_file = $this->find_main_html_file($app_dir);

            if (!$new_main_file) {
                wp_send_json_error('No suitable main HTML file found');
            }

            // Update database
            $result = $wpdb->update(
                $this->table_name,
                ['main_file' => $new_main_file],
                ['id' => $app_id]
            );

            if ($result !== false) {
                wp_send_json_success(['main_file' => $new_main_file]);
            } else {
                wp_send_json_error('Failed to update database');
            }
        }
    }

    public function handle_subdomain_request() {
        // Only run once per request
        static $handled = false;
        if ($handled) {
            return;
        }
        $handled = true;

        $host = $_SERVER['HTTP_HOST'];
        $main_host = parse_url(home_url(), PHP_URL_HOST);

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("HTML App Manager - Host: $host, Main Host: $main_host");
        }

        // Extract subdomain - handle both www and non-www cases
        $subdomain = str_replace(['www.', '.' . $main_host], '', $host);

        // Skip if this is the main domain
        if ($subdomain === $main_host || $subdomain === 'www' || empty($subdomain) || $subdomain === $host) {
            return;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("HTML App Manager - Detected subdomain: $subdomain");
        }

        global $wpdb;
        $app = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE subdomain = %s", $subdomain)
        );

        if ($app) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("HTML App Manager - Found app: {$app->app_name} (Type: {$app->app_type})");
            }

            // Check if this is a request for a specific file in a file-based app
            $request_uri = $_SERVER['REQUEST_URI'];

            // Clean up the request URI
            $request_uri = parse_url($request_uri, PHP_URL_PATH);

            if (property_exists($app, 'app_type') && $app->app_type === 'files' && $request_uri !== '/') {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("HTML App Manager - Serving file: $request_uri");
                }
                $this->serve_app_file($app, $request_uri);
                exit;
            }

            // Increment visit count for main page requests only
            if ($request_uri === '/') {
                $wpdb->query(
                    $wpdb->prepare(
                        "UPDATE {$this->table_name} SET visit_count = visit_count + 1 WHERE id = %d",
                        $app->id
                    )
                );
            }

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("HTML App Manager - Rendering app");
            }

            $this->render_app($app);
            exit;
        } else {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("HTML App Manager - No app found for subdomain: $subdomain");
            }
        }
    }

    /**
     * Serve individual files for file-based apps
     */
    private function serve_app_file($app, $request_uri) {
        if (!$app->file_path) {
            status_header(404);
            exit;
        }

        $upload_dir = wp_upload_dir();
        $app_dir = $upload_dir['basedir'] . '/web-apps/' . $app->file_path;

        // Clean the request URI and remove leading slash
        $file_path = ltrim($request_uri, '/');

        // Prevent directory traversal attacks
        $file_path = str_replace(['../', '..\\'], '', $file_path);

        // First, try the direct path
        $full_file_path = $app_dir . '/' . $file_path;

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("HTML App Manager - Trying to serve: $full_file_path");
        }

        // If file doesn't exist at root level, check if main file is in a subdirectory
        if (!file_exists($full_file_path) || !is_file($full_file_path)) {
            // Get the directory of the main file
            $main_file_dir = dirname($app->main_file);

            if ($main_file_dir && $main_file_dir !== '.') {
                // Try looking in the same directory as the main file
                $alt_file_path = $app_dir . '/' . $main_file_dir . '/' . $file_path;

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("HTML App Manager - Trying alternative path: $alt_file_path");
                }

                if (file_exists($alt_file_path) && is_file($alt_file_path)) {
                    $full_file_path = $alt_file_path;
                } else {
                    // Still not found, return 404
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("HTML App Manager - File not found in either location: $full_file_path or $alt_file_path");
                    }
                    status_header(404);
                    exit;
                }
            } else {
                // Main file is at root, but requested file not found
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("HTML App Manager - File not found: $full_file_path");
                }
                status_header(404);
                exit;
            }
        }

        // Get file extension and set appropriate content type
        $file_extension = strtolower(pathinfo($full_file_path, PATHINFO_EXTENSION));
        $content_types = [
            'html' => 'text/html',
            'htm' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'jsx' => 'application/javascript',
            'ts' => 'application/typescript',
            'tsx' => 'application/typescript',
            'vue' => 'text/x-vue',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'txt' => 'text/plain',
            'md' => 'text/markdown',
            'map' => 'application/json',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'webp' => 'image/webp',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject',
            'mp3' => 'audio/mpeg',
            'mp4' => 'video/mp4',
            'wav' => 'audio/wav',
            'ogg' => 'audio/ogg',
            'webm' => 'video/webm',
            'pdf' => 'application/pdf',
            'csv' => 'text/csv'
        ];

        $content_type = isset($content_types[$file_extension]) ? $content_types[$file_extension] : 'application/octet-stream';

        // Clear any previous headers
        if (!headers_sent()) {
            header('Content-Type: ' . $content_type);
            header('Content-Length: ' . filesize($full_file_path));

            // Add CORS headers for cross-origin requests
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET');
            header('Access-Control-Allow-Headers: Content-Type');
        }

        // Set cache headers for static assets
        if (in_array($file_extension, ['css', 'js', 'jsx', 'ts', 'tsx', 'vue', 'json', 'map', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico', 'webp', 'woff', 'woff2', 'ttf', 'eot', 'mp3', 'mp4', 'wav', 'ogg', 'webm'])) {
            header('Cache-Control: public, max-age=31536000');
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
        }

        readfile($full_file_path);
        exit;
    }

    private function render_app($app) {
        // Handle file-based apps
        if (property_exists($app, 'app_type') && $app->app_type === 'files') {
            $this->render_file_based_app($app);
            return;
        }

        // Check if this is a single file app (check if property exists first)
        if ((property_exists($app, 'app_type') && $app->app_type === 'single') ||
            (property_exists($app, 'is_single_file') && $app->is_single_file)) {
            if (property_exists($app, 'single_html_content') && !empty($app->single_html_content)) {
                // Output the single HTML file content directly
                echo $app->single_html_content;
                return;
            }
        }

        // Otherwise render the traditional way with separate HTML/CSS/JS
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html($app->app_name); ?></title>
    <style><?php echo $app->css_content; ?></style>
</head>
<body>
    <?php echo $app->html_content; ?>
    <?php if (!empty($app->js_content)) : ?>
        <script>
            <?php echo html_entity_decode($app->js_content); ?>
        </script>
    <?php endif; ?>
</body>
</html>
<?php
    }

    /**
     * Render file-based apps
     */
    private function render_file_based_app($app) {
        if (!$app->file_path || !$app->main_file) {
            echo '<h1>App Error</h1><p>App files not found.</p>';
            return;
        }

        $upload_dir = wp_upload_dir();
        $app_dir = $upload_dir['basedir'] . '/web-apps/' . $app->file_path;
        $main_file_path = $app_dir . '/' . $app->main_file;

        if (!file_exists($main_file_path)) {
            echo '<h1>App Error</h1><p>Main app file not found.</p>';
            return;
        }

        // Check if debug mode is requested
        if (isset($_GET['debug']) && $_GET['debug'] === 'html') {
            $this->show_html_debug($app, $main_file_path);
            return;
        }

        // Read and output the main HTML file
        $content = file_get_contents($main_file_path);

        // Replace relative paths to work with subdomain structure
        $content = $this->process_html_content($content, $app);

        echo $content;
    }

    /**
     * Show HTML debug information
     */
    private function show_html_debug($app, $main_file_path) {
        echo "<h1>HTML Debug for {$app->app_name}</h1>";
        echo "<p><strong>Main File:</strong> {$app->main_file}</p>";
        echo "<p><strong>File Path:</strong> {$app->file_path}</p>";
        echo "<p><strong>Full Path:</strong> $main_file_path</p>";

        $main_file_dir = dirname($app->main_file);
        echo "<p><strong>Main File Directory:</strong> '$main_file_dir'</p>";

        if ($main_file_dir && $main_file_dir !== '.') {
            $host = $_SERVER['HTTP_HOST'];
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $base_url = "{$protocol}://{$host}/{$main_file_dir}/";
            echo "<p><strong>Base URL:</strong> $base_url</p>";
        }

        $original_content = file_get_contents($main_file_path);
        $processed_content = $this->process_html_content($original_content, $app);

        echo "<h2>Original HTML (first 1000 chars)</h2>";
        echo "<textarea style='width:100%; height:200px;'>" . htmlspecialchars(substr($original_content, 0, 1000)) . "</textarea>";

        echo "<h2>Processed HTML (first 1000 chars)</h2>";
        echo "<textarea style='width:100%; height:200px;'>" . htmlspecialchars(substr($processed_content, 0, 1000)) . "</textarea>";

        echo "<p><a href='?' style='background:#0073aa; color:white; padding:10px; text-decoration:none;'>← Back to App</a></p>";
    }

    /**
     * Process HTML content to fix relative paths
     */
    private function process_html_content($content, $app) {
        // Get the directory of the main file
        $main_file_dir = dirname($app->main_file);

        // If the main file is in a subdirectory, we need to fix relative paths
        if ($main_file_dir && $main_file_dir !== '.') {
            $host = $_SERVER['HTTP_HOST'];
            // Use the same protocol as the current request
            $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ||
                       (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
                       (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443) ? 'https' : 'http';
            $base_url = "{$protocol}://{$host}/{$main_file_dir}/";

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("HTML App Manager - Processing HTML for subdirectory: $main_file_dir");
                error_log("HTML App Manager - Base URL: $base_url");
            }

            // Use ONLY the base tag approach to avoid double paths
            $base_tag = "<base href=\"{$base_url}\">";

            // Look for existing base tag first
            if (!preg_match('/<base\s+[^>]*>/i', $content)) {
                // Try to insert after <head> tag
                if (preg_match('/<head[^>]*>/i', $content)) {
                    $content = preg_replace('/(<head[^>]*>)/i', '$1' . "\n    " . $base_tag, $content, 1);
                } else if (preg_match('/<html[^>]*>/i', $content)) {
                    // If no head tag, create one
                    $content = preg_replace('/(<html[^>]*>)/i', '$1' . "\n<head>\n    " . $base_tag . "\n</head>", $content, 1);
                } else {
                    // If no html tag, prepend to content
                    $content = "<head>\n    " . $base_tag . "\n</head>\n" . $content;
                }
            }

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("HTML App Manager - Added base tag: $base_tag");
            }
        }

        return $content;
    }
}

new Web_App_Manager();
