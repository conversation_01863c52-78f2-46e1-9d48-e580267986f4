// Simple React-like implementation for demonstration
(function() {
    'use strict';
    
    // Simple state management
    let appState = {
        counter: 0,
        message: 'Welcome to the React App hosted by Web App Manager!',
        features: [
            {
                title: 'React Support',
                description: 'Full support for React applications with JSX and modern JavaScript features.'
            },
            {
                title: 'Easy Deployment',
                description: 'Simply upload your build folder as a ZIP file and deploy instantly.'
            },
            {
                title: 'Framework Detection',
                description: 'Automatic detection of React version and dependencies from package.json.'
            }
        ]
    };
    
    // Simple React-like createElement function
    function createElement(type, props, ...children) {
        const element = document.createElement(type);
        
        if (props) {
            Object.keys(props).forEach(key => {
                if (key === 'className') {
                    element.className = props[key];
                } else if (key.startsWith('on') && typeof props[key] === 'function') {
                    const eventType = key.substring(2).toLowerCase();
                    element.addEventListener(eventType, props[key]);
                } else {
                    element.setAttribute(key, props[key]);
                }
            });
        }
        
        children.forEach(child => {
            if (typeof child === 'string' || typeof child === 'number') {
                element.appendChild(document.createTextNode(child));
            } else if (child && child.nodeType) {
                element.appendChild(child);
            }
        });
        
        return element;
    }
    
    // Component functions
    function Header() {
        return createElement('div', { className: 'App-header' },
            createElement('div', { className: 'App-logo' }, '⚛️'),
            createElement('h1', { className: 'App-title' }, 'React App'),
            createElement('p', { className: 'App-subtitle' }, appState.message)
        );
    }
    
    function FeatureCard(feature) {
        return createElement('div', { className: 'feature-card' },
            createElement('h3', null, feature.title),
            createElement('p', null, feature.description)
        );
    }
    
    function Counter() {
        return createElement('div', null,
            createElement('h2', null, 'Interactive Counter Demo'),
            createElement('div', { className: 'counter' }, appState.counter),
            createElement('button', {
                className: 'demo-button',
                onClick: () => {
                    appState.counter++;
                    render();
                }
            }, 'Increment'),
            createElement('button', {
                className: 'demo-button',
                onClick: () => {
                    appState.counter--;
                    render();
                }
            }, 'Decrement'),
            createElement('button', {
                className: 'demo-button',
                onClick: () => {
                    appState.counter = 0;
                    render();
                }
            }, 'Reset')
        );
    }
    
    function StatusMessage() {
        return createElement('div', { className: 'status-message status-success' },
            '✅ React app loaded successfully! Framework detected and served by Web App Manager.'
        );
    }
    
    function InfoMessage() {
        return createElement('div', { className: 'status-message status-info' },
            'ℹ️ This is a demonstration React application. The Web App Manager plugin automatically detected this as a React app from the package.json file.'
        );
    }
    
    function App() {
        const featureGrid = createElement('div', { className: 'feature-grid' });
        appState.features.forEach(feature => {
            featureGrid.appendChild(FeatureCard(feature));
        });
        
        return createElement('div', { className: 'App' },
            Header(),
            createElement('div', { className: 'App-content' },
                StatusMessage(),
                InfoMessage(),
                Counter(),
                createElement('h2', null, 'Features'),
                featureGrid,
                createElement('div', { style: 'margin-top: 30px;' },
                    createElement('button', {
                        className: 'demo-button',
                        onClick: () => {
                            alert('Hello from React! 🚀\n\nThis React app is being served by the Web App Manager WordPress plugin.\n\nFeatures:\n• Automatic framework detection\n• Proper MIME type handling\n• Modern JavaScript support\n• Easy deployment via ZIP upload');
                        }
                    }, 'Show Info'),
                    createElement('button', {
                        className: 'demo-button',
                        onClick: () => {
                            window.open('https://reactjs.org/', '_blank');
                        }
                    }, 'Learn React')
                )
            )
        );
    }
    
    // Render function
    function render() {
        const root = document.getElementById('root');
        root.innerHTML = '';
        root.appendChild(App());
    }
    
    // Initialize the app
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 React App Loading...');
        console.log('📦 Framework: React ^18.2.0');
        console.log('🔧 Hosted by: Web App Manager WordPress Plugin');
        
        render();
        
        console.log('✅ React App Loaded Successfully!');
        
        // Add some interactive console commands for demonstration
        window.reactDemo = {
            increment: () => {
                appState.counter++;
                render();
                console.log('Counter incremented to:', appState.counter);
            },
            setMessage: (msg) => {
                appState.message = msg;
                render();
                console.log('Message updated to:', msg);
            },
            getState: () => {
                console.log('Current app state:', appState);
                return appState;
            }
        };
        
        console.log('🎮 Try these commands in the console:');
        console.log('reactDemo.increment() - Increment the counter');
        console.log('reactDemo.setMessage("Your message") - Change the header message');
        console.log('reactDemo.getState() - View current app state');
    });
})();
