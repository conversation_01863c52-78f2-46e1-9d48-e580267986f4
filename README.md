# Web App Manager - WordPress Plugin

## Overview
The Web App Manager is a powerful WordPress plugin that allows you to host multiple web applications with custom subdomains. It supports modern frameworks and traditional web technologies:

1. **Separate HTML/CSS/JS** - Traditional approach with separate content fields
2. **Single HTML File** - Complete HTML file with embedded CSS and JavaScript
3. **Modern Framework Apps** - Upload ZIP files containing React, Angular, Vue.js, Svelte, and other framework applications

## New Features in Version 5.0

### Modern Framework Support
- **React** - Including JSX/TSX files and Next.js applications
- **Angular** - Full TypeScript support with Angular CLI builds
- **Vue.js** - Support for .vue files and Nuxt.js applications
- **Svelte** - Including SvelteKit applications
- **Ember.js** - Full Ember application support
- **Preact** - Lightweight React alternative
- **jQuery** - Classic jQuery applications
- **And many more!**

### Enhanced Framework Detection
- Automatic framework detection from package.json
- Framework-specific optimizations
- Version tracking and display
- Build artifact support

## Previous Features (Version 4.0)

### File-based App Support
- Upload ZIP files containing complex HTML applications
- Support for multiple HTML, CSS, and JavaScript files
- Asset support: images, fonts, audio, video files
- Automatic main file detection (index.html, main.html, etc.)
- Proper file serving with correct MIME types
- Directory structure preservation

### Supported File Types
- **Web Files**: .html, .htm, .css, .js, .jsx, .ts, .tsx, .vue
- **Images**: .png, .jpg, .jpeg, .gif, .svg, .ico, .webp
- **Fonts**: .woff, .woff2, .ttf, .eot
- **Media**: .mp3, .mp4, .wav, .ogg, .webm
- **Data**: .json, .xml, .csv, .txt, .md
- **Build Files**: .map (source maps)

### Enhanced Database Schema
- New `app_type` field to distinguish between app types
- `file_path` field to store uploaded app directory
- `main_file` field to identify the entry point
- `framework` field to store detected framework type
- `framework_version` field to track framework versions
- Automatic migration of existing apps and data

## Usage

### Creating a Modern Web App
1. Go to WordPress Admin → Web Apps
2. Click "Add New App"
3. Enter app name and subdomain
4. Select "Upload ZIP File (Complex App)" as app type
5. Upload your ZIP file (max 50MB)
6. The plugin will automatically detect your framework
7. Save the app

### Framework-Specific Examples

#### React App (Create React App)
```
my-react-app.zip
├── index.html
├── static/
│   ├── css/
│   │   └── main.css
│   └── js/
│       └── main.js
├── manifest.json
└── favicon.ico
```

#### Angular App (ng build)
```
my-angular-app.zip
├── index.html
├── main.js
├── polyfills.js
├── runtime.js
├── styles.css
└── assets/
    └── images/
```

#### Vue.js App (npm run build)
```
my-vue-app.zip
├── index.html
├── css/
│   └── app.css
├── js/
│   └── app.js
└── img/
    └── logo.png
```

### File Serving
- Main app loads from: `http://subdomain.yoursite.com/`
- Assets load from: `http://subdomain.yoursite.com/path/to/asset`
- Automatic MIME type detection for all supported file types
- Cache headers for static assets
- CORS headers for cross-origin requests
- Framework-specific optimizations

### Framework Build Instructions

#### React (Create React App)
```bash
npm run build
# Upload the contents of the 'build' folder as ZIP
```

#### Angular
```bash
ng build --prod
# Upload the contents of the 'dist/your-app-name' folder as ZIP
```

#### Vue.js
```bash
npm run build
# Upload the contents of the 'dist' folder as ZIP
```

#### Svelte
```bash
npm run build
# Upload the contents of the 'public' folder as ZIP
```

## Technical Details

### Directory Structure
Uploaded apps are stored in: `wp-content/uploads/web-apps/app_[ID]/`

### Security Features
- File type validation
- Directory traversal protection
- Nonce verification for all operations
- File size limits (50MB max)

### Cleanup
- Automatic file cleanup when apps are deleted
- Optional cleanup on plugin deactivation

## Installation
1. Upload the plugin to `/wp-content/plugins/html-app-manager/`
2. Activate the plugin through WordPress admin
3. Configure your apps via Web Apps menu

## Requirements
- WordPress 5.0+
- PHP 7.4+
- ZipArchive PHP extension
- File upload permissions

## Changelog

### Version 5.0
- **MAJOR UPDATE**: Renamed to Web App Manager
- Added support for modern frameworks: React, Angular, Vue.js, Svelte, Ember.js, Preact
- Automatic framework detection from package.json and file structure
- Enhanced file type support: JSX, TSX, Vue, TypeScript, source maps
- Updated admin interface with framework information display
- Improved upload guidance for different framework types
- Database migration from html_apps to web_apps table
- Directory migration from html-apps to web-apps

### Version 4.0
- Added support for complex file-based apps via ZIP upload
- Enhanced database schema with app_type, file_path, and main_file fields
- Implemented file serving system with proper MIME types
- Added automatic file cleanup on app deletion
- Updated admin interface with file upload option
- Improved validation and error handling

### Version 3.8
- Previous version with basic HTML/CSS/JS and single file support
