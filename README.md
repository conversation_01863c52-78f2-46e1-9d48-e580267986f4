# HTML App Manager - WordPress Plugin

## Overview
The HTML App Manager is a powerful WordPress plugin that allows you to host multiple HTML applications with custom subdomains. It now supports three different types of apps:

1. **Separate HTML/CSS/JS** - Traditional approach with separate content fields
2. **Single HTML File** - Complete HTML file with embedded CSS and JavaScript
3. **Complex File-based Apps** - Upload ZIP files containing complete applications with multiple files, folders, and assets

## New Features in Version 4.0

### File-based App Support
- Upload ZIP files containing complex HTML applications
- Support for multiple HTML, CSS, and JavaScript files
- Asset support: images, fonts, audio, video files
- Automatic main file detection (index.html, main.html, etc.)
- Proper file serving with correct MIME types
- Directory structure preservation

### Supported File Types
- **HTML**: .html, .htm
- **Stylesheets**: .css
- **Scripts**: .js
- **Images**: .png, .jpg, .jpeg, .gif, .svg, .ico
- **Fonts**: .woff, .woff2, .ttf, .eot
- **Media**: .mp3, .mp4, .wav, .ogg
- **Data**: .json

### Enhanced Database Schema
- New `app_type` field to distinguish between app types
- `file_path` field to store uploaded app directory
- `main_file` field to identify the entry point
- Automatic migration of existing apps

## Usage

### Creating a File-based App
1. Go to WordPress Admin → HTML Apps
2. Click "Add New App"
3. Enter app name and subdomain
4. Select "Upload ZIP File (Complex App)" as app type
5. Upload your ZIP file (max 50MB)
6. Save the app

### ZIP File Structure
Your ZIP file should contain:
```
your-app.zip
├── index.html (or main.html)
├── css/
│   └── styles.css
├── js/
│   └── script.js
├── images/
│   ├── logo.png
│   └── background.jpg
├── audio/
│   └── sound.mp3
└── data/
    └── config.json
```

### File Serving
- Main app loads from: `http://subdomain.yoursite.com/`
- Assets load from: `http://subdomain.yoursite.com/path/to/asset`
- Automatic MIME type detection
- Cache headers for static assets

## Technical Details

### Directory Structure
Uploaded apps are stored in: `wp-content/uploads/html-apps/app_[ID]/`

### Security Features
- File type validation
- Directory traversal protection
- Nonce verification for all operations
- File size limits (50MB max)

### Cleanup
- Automatic file cleanup when apps are deleted
- Optional cleanup on plugin deactivation

## Installation
1. Upload the plugin to `/wp-content/plugins/html-app-manager/`
2. Activate the plugin through WordPress admin
3. Configure your apps via HTML Apps menu

## Requirements
- WordPress 5.0+
- PHP 7.4+
- ZipArchive PHP extension
- File upload permissions

## Changelog

### Version 4.0
- Added support for complex file-based apps via ZIP upload
- Enhanced database schema with app_type, file_path, and main_file fields
- Implemented file serving system with proper MIME types
- Added automatic file cleanup on app deletion
- Updated admin interface with file upload option
- Improved validation and error handling

### Version 3.8
- Previous version with basic HTML/CSS/JS and single file support
