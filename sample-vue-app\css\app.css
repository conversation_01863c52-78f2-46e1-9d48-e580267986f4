body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

#app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.vue-container {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  overflow: hidden;
  max-width: 800px;
  width: 100%;
}

.vue-header {
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  color: white;
  padding: 40px;
  text-align: center;
}

.vue-logo {
  font-size: 4rem;
  margin-bottom: 20px;
  display: block;
}

.vue-title {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 300;
}

.vue-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

.vue-content {
  padding: 40px;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.counter-display {
  font-size: 3rem;
  font-weight: bold;
  color: #42b883;
  text-align: center;
  margin: 20px 0;
}

.button-group {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
  margin: 20px 0;
}

.vue-button {
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 100px;
}

.vue-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(66, 184, 131, 0.4);
}

.vue-button:active {
  transform: translateY(0);
}

.vue-button.secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.vue-button.secondary:hover {
  box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.feature-card {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  border-left: 4px solid #42b883;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card h3 {
  margin-top: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.feature-card p {
  color: #6c757d;
  margin-bottom: 0;
  line-height: 1.6;
}

.status-message {
  padding: 20px;
  border-radius: 10px;
  margin: 20px 0;
  font-weight: 500;
}

.status-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.todo-section {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  margin: 30px 0;
}

.todo-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 15px;
  transition: border-color 0.3s ease;
}

.todo-input:focus {
  outline: none;
  border-color: #42b883;
}

.todo-list {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.todo-item {
  background: white;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.todo-item.completed {
  opacity: 0.6;
  text-decoration: line-through;
}

.todo-text {
  flex-grow: 1;
  margin-right: 15px;
}

.todo-actions {
  display: flex;
  gap: 10px;
}

.todo-button {
  background: #42b883;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.todo-button.delete {
  background: #dc3545;
}

.todo-button:hover {
  opacity: 0.8;
}

@media (max-width: 768px) {
  .vue-header {
    padding: 30px 20px;
  }
  
  .vue-title {
    font-size: 2rem;
  }
  
  .vue-content {
    padding: 30px 20px;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .button-group {
    flex-direction: column;
    align-items: center;
  }
  
  .vue-button {
    width: 200px;
  }
}
