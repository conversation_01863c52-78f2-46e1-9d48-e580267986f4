// Simple Vue.js-like implementation for demonstration
(function() {
    'use strict';
    
    // Application state
    const appState = {
        counter: 0,
        message: 'Welcome to Vue.js hosted by Web App Manager!',
        newTodo: '',
        todos: [
            { id: 1, text: 'Learn Vue.js', completed: false },
            { id: 2, text: 'Build awesome apps', completed: false },
            { id: 3, text: 'Deploy with Web App Manager', completed: true }
        ],
        features: [
            {
                title: 'Vue.js Support',
                description: 'Full support for Vue.js applications with reactive data binding and component system.'
            },
            {
                title: 'Modern JavaScript',
                description: 'ES6+ features, modules, and modern JavaScript syntax fully supported.'
            },
            {
                title: 'Auto Detection',
                description: 'Framework automatically detected from package.json and file structure.'
            },
            {
                title: 'Easy Deployment',
                description: 'Upload your dist folder as ZIP and deploy instantly to custom subdomain.'
            }
        ]
    };
    
    let nextTodoId = 4;
    
    // Simple reactive system
    const watchers = [];
    
    function reactive(obj) {
        return new Proxy(obj, {
            set(target, key, value) {
                target[key] = value;
                watchers.forEach(watcher => watcher());
                return true;
            }
        });
    }
    
    const state = reactive(appState);
    
    // Component functions
    function createHeader() {
        return `
            <div class="vue-header">
                <div class="vue-logo">🟢</div>
                <h1 class="vue-title">Vue.js App</h1>
                <p class="vue-subtitle">${state.message}</p>
            </div>
        `;
    }
    
    function createStatusMessages() {
        return `
            <div class="status-message status-success">
                ✅ Vue.js app loaded successfully! Framework detected and served by Web App Manager.
            </div>
            <div class="status-message status-info">
                ℹ️ This is a demonstration Vue.js application. The Web App Manager plugin automatically detected this as a Vue.js app from the package.json file.
            </div>
        `;
    }
    
    function createCounter() {
        return `
            <div class="demo-section">
                <h2>Interactive Counter Demo</h2>
                <div class="counter-display">${state.counter}</div>
                <div class="button-group">
                    <button class="vue-button" onclick="vueApp.increment()">Increment</button>
                    <button class="vue-button" onclick="vueApp.decrement()">Decrement</button>
                    <button class="vue-button secondary" onclick="vueApp.reset()">Reset</button>
                </div>
            </div>
        `;
    }
    
    function createFeatures() {
        const featuresHtml = state.features.map(feature => `
            <div class="feature-card">
                <h3>${feature.title}</h3>
                <p>${feature.description}</p>
            </div>
        `).join('');
        
        return `
            <div class="demo-section">
                <h2>Features</h2>
                <div class="feature-grid">
                    ${featuresHtml}
                </div>
            </div>
        `;
    }
    
    function createTodoApp() {
        const todosHtml = state.todos.map(todo => `
            <li class="todo-item ${todo.completed ? 'completed' : ''}">
                <span class="todo-text">${todo.text}</span>
                <div class="todo-actions">
                    <button class="todo-button" onclick="vueApp.toggleTodo(${todo.id})">
                        ${todo.completed ? 'Undo' : 'Done'}
                    </button>
                    <button class="todo-button delete" onclick="vueApp.deleteTodo(${todo.id})">Delete</button>
                </div>
            </li>
        `).join('');
        
        return `
            <div class="demo-section">
                <div class="todo-section">
                    <h2>Todo List Demo</h2>
                    <input 
                        type="text" 
                        class="todo-input" 
                        placeholder="Add a new todo..." 
                        value="${state.newTodo}"
                        onkeyup="vueApp.handleTodoInput(event)"
                    >
                    <button class="vue-button" onclick="vueApp.addTodo()">Add Todo</button>
                    <ul class="todo-list">
                        ${todosHtml}
                    </ul>
                </div>
            </div>
        `;
    }
    
    function createActionButtons() {
        return `
            <div class="demo-section">
                <div class="button-group">
                    <button class="vue-button" onclick="vueApp.showInfo()">Show Info</button>
                    <button class="vue-button" onclick="vueApp.changeMessage()">Change Message</button>
                    <button class="vue-button secondary" onclick="window.open('https://vuejs.org/', '_blank')">Learn Vue.js</button>
                </div>
            </div>
        `;
    }
    
    // Main app component
    function createApp() {
        return `
            <div class="vue-container">
                ${createHeader()}
                <div class="vue-content">
                    ${createStatusMessages()}
                    ${createCounter()}
                    ${createTodoApp()}
                    ${createFeatures()}
                    ${createActionButtons()}
                </div>
            </div>
        `;
    }
    
    // App methods
    const vueApp = {
        increment() {
            state.counter++;
        },
        
        decrement() {
            state.counter--;
        },
        
        reset() {
            state.counter = 0;
        },
        
        handleTodoInput(event) {
            state.newTodo = event.target.value;
            if (event.key === 'Enter') {
                this.addTodo();
            }
        },
        
        addTodo() {
            if (state.newTodo.trim()) {
                state.todos.push({
                    id: nextTodoId++,
                    text: state.newTodo.trim(),
                    completed: false
                });
                state.newTodo = '';
            }
        },
        
        toggleTodo(id) {
            const todo = state.todos.find(t => t.id === id);
            if (todo) {
                todo.completed = !todo.completed;
                // Trigger reactivity manually for nested objects
                state.todos = [...state.todos];
            }
        },
        
        deleteTodo(id) {
            state.todos = state.todos.filter(t => t.id !== id);
        },
        
        showInfo() {
            alert(`Hello from Vue.js! 🟢

This Vue.js app is being served by the Web App Manager WordPress plugin.

Features:
• Automatic framework detection
• Reactive data binding
• Component-based architecture
• Modern JavaScript support
• Easy deployment via ZIP upload

Current state:
• Counter: ${state.counter}
• Todos: ${state.todos.length}
• Framework: Vue.js ^3.3.4`);
        },
        
        changeMessage() {
            const messages = [
                'Welcome to Vue.js hosted by Web App Manager!',
                'Vue.js makes building UIs simple and fun! 🎉',
                'Reactive, component-based, and awesome! ⚡',
                'Built with Vue.js and deployed instantly! 🚀',
                'The Progressive JavaScript Framework! 💚'
            ];
            const currentIndex = messages.indexOf(state.message);
            const nextIndex = (currentIndex + 1) % messages.length;
            state.message = messages[nextIndex];
        },
        
        getState() {
            return state;
        }
    };
    
    // Render function
    function renderApp() {
        const app = document.getElementById('app');
        app.innerHTML = createApp();
    }
    
    // Watch for state changes
    watchers.push(renderApp);
    
    // Make vueApp globally available
    window.vueApp = vueApp;
    
    // Initialize the app
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🟢 Vue.js App Loading...');
        console.log('📦 Framework: Vue.js ^3.3.4');
        console.log('🔧 Hosted by: Web App Manager WordPress Plugin');
        
        renderApp();
        
        console.log('✅ Vue.js App Loaded Successfully!');
        
        // Add console commands for demonstration
        window.vueDemo = {
            increment: () => vueApp.increment(),
            addTodo: (text) => {
                state.newTodo = text;
                vueApp.addTodo();
            },
            getState: () => vueApp.getState(),
            changeMessage: () => vueApp.changeMessage()
        };
        
        console.log('🎮 Try these commands in the console:');
        console.log('vueDemo.increment() - Increment the counter');
        console.log('vueDemo.addTodo("Your todo") - Add a new todo');
        console.log('vueDemo.changeMessage() - Change the header message');
        console.log('vueDemo.getState() - View current app state');
    });
})();
